<?php
/**
 * Template Name: Articles and Podcasts Page
 */

?>
<?php get_header(); ?>
<?php pearl_get_titlebox(); ?>

<?php if (have_posts()) : ?>
    <?php
    $current_object = get_queried_object();

    $post_type = 'post';
    if (!is_wp_error($current_object) and !empty($current_object) and !empty($current_object->name)) {
        $post_type = $current_object->name;
    }
    if (!is_wp_error($current_object) and !empty($current_object) and !empty($current_object->term_id)) {
        $post_type = pearl_get_post_type_by_taxonomy($current_object->taxonomy);
        $post_type = $post_type[0];
        $settings = pearl_get_post_settings($post_type);
        $style_path = '/assets/css/post_types/' . str_replace('stm_', '', $post_type) . '/' . $settings['style'] . '.css';
        $theme_info = pearl_get_assets_path();


        if (file_exists(get_template_directory() . $style_path)) {
            wp_enqueue_style($post_type . '_archive', get_template_directory_uri() . $style_path, null, $theme_info['v']);
        } else {
            $post_type = 'post';
        }
    }


    $settings = pearl_get_post_settings($post_type);

    $sidebar_position = pearl_get_sidebar_setting($post_type);
    $sidebar_mobile = pearl_get_sidebar_mobile($post_type, 'archive');
    if (!pearl_has_sidebar()) $sidebar_position = 'full';

    $wrapper_classes = array('stm_markup', 'stm_markup_' . esc_attr($sidebar_position));

    if ($sidebar_mobile === 'hidden') {
        $wrapper_classes[] = 'stm_sidebar_hidden';
    }
    if (!empty($current_object->ID)) {
        $breadcrumbs = get_post_meta($current_object->ID, 'page_bc', true);
        $settings['breadcrumbs'] = $breadcrumbs;
    }

    if (pearl_check_string($settings['breadcrumbs'])) : ?>
        <div class="stm_page_bc container">
            <?php get_template_part('partials/global/breadcrumbs'); ?>
        </div>
    <?php endif;

    if (!file_exists(get_template_directory() . $settings['tpl'] . '.php')) {
        $settings['tpl'] = '/partials/content/post/archive_layouts/list/style_1';
    };

    $paged1 = isset($_GET['paged1']) ? (int) $_GET['paged1'] : 1;
    $paged2 = isset($_GET['paged2']) ? (int) $_GET['paged2'] : 1;

    $args1 = array(
        'category_name' => 'articles',
        'paged'          => $paged1,
        'posts_per_page' => 9,
    );

    $query1 = new WP_Query($args1);

    $pag_args1 = array(
        'type' => 'list',
        'format'  => '?paged1=%#%',
        'current' => $paged1,
        'total'   => $query1->max_num_pages,
        'add_args' => array('paged2' => $paged2)
    ); ?>

    <h2 class="pod-h-1">Articles</h2>
    <div class="<?php echo esc_attr(implode(' ', $wrapper_classes)) ?>">

        <div class="stm_markup__content stm_markup__<?php echo esc_attr($post_type); ?>">
            <div class="stm_loop stm_loop__<?php echo esc_attr($settings['view_type']); ?>">
                <? while ($query1->have_posts()) : $query1->the_post();
                    get_template_part($settings['tpl']);
                endwhile; ?>
            </div>
            <?php
            echo pearl_pagination($pag_args1);
            ?>
        </div>

        <?php

        if ('full' !== $sidebar_position) : ?>
            <div class="stm_markup__sidebar stm_markup__sidebar_divider stm_markup__sidebar_archive">
                <div class="sidebar_inner">
                    <?php pearl_sidebar(); ?>
                </div>
            </div>
        <?php endif; ?>

    </div>

    <? $args2 = array(
        'category_name' => 'podcasts',
        'paged'          => $paged2,
        'posts_per_page' => 9,
    );

    $query2 = new WP_Query($args2);

    $pag_args2 = array(
        'type' => 'list',
        'format'  => '?paged2=%#%',
        'current' => $paged2,
        'total'   => $query2->max_num_pages,
        'add_args' => array('paged1' => $paged1)
    ); ?>

    <h2 class="pod-h-2">Podcasts</h2>
    <div class="<?php echo esc_attr(implode(' ', $wrapper_classes)) ?>">

        <div class="stm_markup__content stm_markup__<?php echo esc_attr($post_type); ?>">
            <div class="stm_loop stm_loop__<?php echo esc_attr($settings['view_type']); ?>">
                <? while ($query2->have_posts()) : $query2->the_post();
                    get_template_part($settings['tpl']);
                endwhile; ?>
            </div>
            <?php
            echo pearl_pagination($pag_args2);
            ?>
        </div>

        <?php

        if ('full' !== $sidebar_position) : ?>
            <div class="stm_markup__sidebar stm_markup__sidebar_divider stm_markup__sidebar_archive">
                <div class="sidebar_inner">
                    <?php pearl_sidebar(); ?>
                </div>
            </div>
        <?php endif; ?>

    </div>
<?php endif; ?>

<?php get_footer(); ?>