<?php

add_action('wp_enqueue_scripts', 'pearl_child_enqueue_parent_styles');
function pearl_child_enqueue_parent_styles()
{
	wp_enqueue_style('child-style', get_stylesheet_uri(),[],'128');
}

add_action('add_meta_boxes', 'pearl_786_post_options_metabox');
function pearl_786_post_options_metabox()
{
	add_meta_box('pearl_786_post_options', __('Post Preview Text'), 'pearl_786_post_options_code', 'post', 'side', 'high');
}

function pearl_786_post_options_code($post)
{
	wp_nonce_field("_pearl_786_post_options_noncename", '_pearl_786_post_options_noncename');

	$post_excert = get_post_meta($post->ID,  '_pearl_786_post_excert', true);
?>

	<textarea name="_pearl_786_post_excert" id="pearl_post_excert"><?php echo _e($post_excert) ?></textarea>
	<style>
		#pearl_post_excert {
			width: 100%;
			height: 200px;
		}
	</style>
<?php
}

add_action('save_post', 'pearl_786_save_post_options');
function pearl_786_save_post_options($post_id)
{
	// verify if this is an auto save routine. 
	// If it is our form has not been submitted, so we dont want to do anything
	if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE)
		return;

	if (!isset($_POST['_pearl_786_post_options_noncename'])) {
		return;
	}

	if (!wp_verify_nonce($_POST['_pearl_786_post_options_noncename'], '_pearl_786_post_options_noncename')) {
		return;
	}

	// Check permissions
	if (!current_user_can('edit_post', $post_id))
		return;

	// OK, we're authenticated: we need to find and save the data
	if ('post' == $_POST['post_type']) {
		update_post_meta($post_id, '_pearl_786_post_excert', $_POST['_pearl_786_post_excert']);
	}
}

add_filter('woocommerce_variable_sale_price_html', 'pearl_786_wc_variation_price_format', 10, 2);
add_filter('woocommerce_variable_price_html', 'pearl_786_wc_variation_price_format', 10, 2);
function pearl_786_wc_variation_price_format($price, $product)
{
	$prices = $product->get_variation_prices(true);
	$max_price = end($prices['price']);
	$price = wc_price($max_price);
	return $price;
}

add_filter('woocommerce_get_price_html', 'pearl_786_wc_price_format', 10, 2);
function pearl_786_wc_price_format($price, $product)
{
	$price = $price . __(' + VAT');
	return $price;
}

add_filter('pearl_site_container', 'pearl_786_site_container');
function pearl_786_site_container($container)
{
	if (is_plugin_active('woocommerce/woocommerce.php')) {
		if (is_product()) {
			$container = 'container';
		}
	}

	return $container;
}

add_action('admin_init', function () {
	// Redirect any user trying to access comments page
	global $pagenow;

	if ($pagenow === 'edit-comments.php') {
		wp_safe_redirect(admin_url());
		exit;
	}

	// Remove comments metabox from dashboard
	remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');

	// Disable support for comments and trackbacks in post types
	foreach (get_post_types() as $post_type) {
		if (post_type_supports($post_type, 'comments')) {
			remove_post_type_support($post_type, 'comments');
			remove_post_type_support($post_type, 'trackbacks');
		}
	}
});

// Close comments on the front-end
add_filter('comments_open', '__return_false', 20, 2);
add_filter('pings_open', '__return_false', 20, 2);

// Hide existing comments
add_filter('comments_array', '__return_empty_array', 10, 2);

// Remove comments page in menu
add_action('admin_menu', function () {
	remove_menu_page('edit-comments.php');
});

// Remove comments links from admin bar
add_action('init', function () {
	if (is_admin_bar_showing()) {
		remove_action('admin_bar_menu', 'wp_admin_bar_comments_menu', 60);
	}
});

// Preload a responsive image only on homepage
function preload_featured_image_home()
{
	if (is_front_page()) {
		echo '<link rel="preload" as="image" href="/wp-content/uploads/2022/02/2.jpg">';
	}
}
add_action('wp_head', 'preload_featured_image_home', 10);

add_filter('lazyload_inline_bg_excludes', function ($exclude_keywords) {
	$exclude_keywords = [
		'bg-image.jpg',
	];
}, 10, 1);

add_filter( 'woocommerce_product_tabs', 'custom_woo_remove_product_tabs', 98 );

function custom_woo_remove_product_tabs( $tabs ) {

    unset( $tabs['additional_information'] );   // Remove the additional information tab

    return $tabs;

}
